<html>
    <head>
        <meta charset="utf-8">
        
            <script src="lib/bindings/utils.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
                <link href="lib/tom-select/tom-select.css" rel="stylesheet">
                <script src="lib/tom-select/tom-select.complete.min.js"></script>
            
        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 750px;
                 background-color: #222222;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
                <div id="select-menu" class="card-header">
                    <div class="row no-gutters">
                        <div class="col-10 pb-2">
                            <select
                            class="form-select"
                            aria-label="Default select example"
                            onchange="selectNode([value]);"
                            id="select-node"
                            placeholder="Select node..."
                            >
                                <option selected>Select a Node by ID</option>
                                
                                    <option value="A">A</option>
                                
                                    <option value="B">B</option>
                                
                                    <option value="C">C</option>
                                
                                    <option value="D">D</option>
                                
                            </select>
                        </div>
                        <div class="col-2 pb-2">
                            <button type="button" class="btn btn-primary btn-block" onclick="neighbourhoodHighlight({nodes: []});">Reset Selection</button>
                        </div>
                    </div>
                </div>
            
            
              <div id="filter-menu" class="card-header">
                <div class="row no-gutters">
                  <div class="col-3 pb-2">
                    <select
                            class="form-select"
                            aria-label="Default select example"
                            onchange="updateFilter(value, 'item')"
                            id="select-item"
                        >
                        <option value="">Select a network item</option>
                        <option value="edge">edge</option>
                        <option value="node">node</option>
                    </select>
                  </div>
                  <div class="col-3 pb-2">
                    <select
                            class="form-select"
                            aria-label="Default select example"
                            onchange="updateFilter(value, 'property')"
                            id="select-property"
                        >
                        <option value="">Select a property...</option>
                    </select>
                  </div>
                  <div class="col-3 pb-2">
                    <select
                            class="form-select"
                            aria-label="Default select example"
                            id="select-value"
                        >
                        <option value="">Select value(s)...</option>
                    </select>
                  </div>
                  <div class="col-1 pb-2">
                    <button type="button" class="btn btn-primary btn-block" onclick="highlightFilter(filter);">Filter</button>
                  </div>
                  <div class="col-2 pb-2">
                    <button type="button" class="btn btn-primary btn-block" onclick="clearFilter(true)">Reset Selection</button>
                  </div>
                </div>
              </div>
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              
                  new TomSelect("#select-node",{
                      create: false,
                      sortField: {
                          field: "text",
                          direction: "asc"
                      }
                  });
              

              
                  // explicitly using onItemAdd and this function as we need to save multiple values
                  let updateValueFilter = function() {
                      return function () {
                      filter['value'].push(arguments[0])
                      }
                  }

                  let valueControl = new TomSelect("#select-value",{
                      maxItems: null,
                      valueField: 'id',
                      labelField: 'title',
                      searchField: 'title',
                      create: false,
                      sortField: {
                          field: "text",
                          direction: "asc"
                      },
                      onItemAdd: updateValueFilter()
                  });

                  let addValues = function() {
                      return function () {
                          // clear the current value options and add the selected attribute values
                          // tom-select handles duplicates
                          let selectedProperty = arguments[0];
                          valueControl.clear();
                          valueControl.clearOptions();
                          filter['value'] = []
                          if (filter['item'] === 'node') {
                              for (let each in allNodes) {
                                  valueControl.addOption({
                                      id:allNodes[each][selectedProperty],
                                      title:allNodes[each][selectedProperty]
                                  })
                              }
                          }
                          else if (filter['item'] === 'edge') {
                              for (let each in allEdges) {
                                  valueControl.addOption({
                                      id:allEdges[each][selectedProperty],
                                      title:allEdges[each][selectedProperty]
                                  })
                              }
                          }
                      }
                  };

                  let propControl = new TomSelect("#select-property",{
                      valueField: 'id',
                      labelField: 'title',
                      searchField: 'title',
                      create: false,
                      sortField: {
                          field: "text",
                          direction: "asc"
                      },
                      onItemAdd: addValues()
                  });

                  let addProperties = function() {
                      return function () {
                          // loops through the selected network item and adds the attributes to dropdown
                          // tom-select handles duplicates
                          clearFilter(false)
                          if (arguments[0] === 'edge') {
                              for (let each in allEdges) {
                                  if (allEdges.hasOwnProperty(each)) {
                                      for (let eachProp in allEdges[each]) {
                                          if (allEdges[each].hasOwnProperty(eachProp)) {
                                              propControl.addOption({id: eachProp, title: eachProp})
                                          }
                                      }
                                  }
                              }
                          }
                          else if (arguments[0] === 'node') {
                              for (let each in allNodes) {
                                  if (allNodes.hasOwnProperty(each)) {
                                      for (let eachProp in allNodes[each]) {
                                          if (allNodes[each].hasOwnProperty(eachProp)
                                              && (eachProp !== 'hidden' && eachProp !== 'savedLabel'
                                                  && eachProp !== 'hiddenLabel')) {
                                              propControl.addOption({id: eachProp, title: eachProp})

                                          }
                                      }
                                  }
                              }
                          }
                      }
                  };

                  let itemControl = new TomSelect("#select-item",{
                      create: false,
                      sortField:{
                          field: "text",
                          direction: "asc"
                      },
                      onItemAdd: addProperties()
                  });

                  function clearFilter(reset) {
                      // utility function to clear all the selected filter options
                      // if reset is set to true, the existing filter will be removed
                      // else, only the dropdown options are cleared
                      propControl.clear();
                      propControl.clearOptions();
                      valueControl.clear();
                      valueControl.clearOptions();
                      filter = {
                          item : '',
                          property : '',
                          value : []
                      }
                      if (reset) {
                          itemControl.clear();
                          filterHighlight({nodes: []})
                      }
                  }

                  function updateFilter(value, key) {
                      // key could be 'item' or 'property' and value is as selected in dropdown
                      filter[key] = value
                  }

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#97c2fc", "font": {"color": "white"}, "id": "A", "label": "A", "shape": "dot", "size": 10, "title": "Node A\u003cbr\u003etype: start"}, {"color": "#ffaa00", "font": {"color": "white"}, "id": "B", "label": "B", "shape": "dot", "size": 10, "title": "Node B\u003cbr\u003etype: middle"}, {"color": "#ffaa00", "font": {"color": "white"}, "id": "C", "label": "C", "shape": "dot", "size": 10, "title": "Node C\u003cbr\u003etype: middle"}, {"color": "#7be19c", "font": {"color": "white"}, "id": "D", "label": "D", "shape": "dot", "size": 10, "title": "Node D\u003cbr\u003etype: end"}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "#848484", "from": "A", "label": "5", "title": "A\u2192B cost=5", "to": "B", "width": 5}, {"arrows": "to", "color": "#ff2e63", "from": "B", "label": "3", "title": "B\u2192A cost=3", "to": "A", "width": 3}, {"arrows": "to", "color": "#848484", "from": "B", "label": "2", "title": "B\u2192C cost=2", "to": "C", "width": 2}, {"arrows": "to", "color": "#ff2e63", "from": "C", "label": "4", "title": "C\u2192B cost=4", "to": "B", "width": 4}, {"arrows": "to", "color": "#848484", "from": "C", "label": "1", "title": "C\u2192D cost=1", "to": "D", "width": 1}, {"arrows": "to", "color": "#ff2e63", "from": "D", "label": "6", "title": "D\u2192C cost=6", "to": "C", "width": 6}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 200}, "barnesHut": {"gravitationalConstant": -8000, "centralGravity": 0.3, "springLength": 150, "springConstant": 0.04}}, "edges": {"smooth": {"type": "curvedCW", "roundness": 0.2}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  
                    network.on("selectNode", neighbourhoodHighlight);
                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>